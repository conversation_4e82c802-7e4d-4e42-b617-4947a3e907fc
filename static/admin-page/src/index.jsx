import React from 'react';
import { createRoot } from 'react-dom/client';
import { invoke, view } from '@forge/bridge';
import MDEditor from '@uiw/react-md-editor';
import './styles.css';


function AdminApp() {
    const [activeTab, setActiveTab] = React.useState('templates');
    
    return (
        <div className="admin-container">
            <TabNavigation activeTab={activeTab} onTabChange={setActiveTab} />
            {activeTab === 'settings' && <SettingsView />}
            {activeTab === 'templates' && <TemplatesView />}
            <StatusMessage />
        </div>
    );
}

function TabNavigation({ activeTab, onTabChange }) {
    const tabs = [
        { id: 'templates', label: 'Templates', description: 'Edit issue type templates' },
        { id: 'settings', label: 'Settings', description: 'Configure AI settings' }
    ];
    
    return (
        <div className="tab-navigation">
            <div className="tab-list">
                {tabs.map(tab => (
                    <button
                        key={tab.id}
                        className={`tab-button ${activeTab === tab.id ? 'active' : ''}`}
                        onClick={() => onTabChange(tab.id)}
                        aria-selected={activeTab === tab.id}
                    >
                        {tab.label}
                    </button>
                ))}
            </div>
        </div>
    );
}

function TemplatesView() {
    const [currentIssueType, setCurrentIssueType] = React.useState('story');
    const [templates, setTemplates] = React.useState({});
    const [currentTemplate, setCurrentTemplate] = React.useState('');
    const [hasUnsavedChanges, setHasUnsavedChanges] = React.useState(false);
    const [isLoading, setIsLoading] = React.useState(true);
    const [availableIssueTypes, setAvailableIssueTypes] = React.useState([]);
    const [confirmModal, setConfirmModal] = React.useState({ isOpen: false, type: '', data: null });
    const [outputFields, setOutputFields] = React.useState({});
    const [currentOutputField, setCurrentOutputField] = React.useState('');
    const [paragraphFields, setParagraphFields] = React.useState([]);
    const [fieldConfigurationData, setFieldConfigurationData] = React.useState({});
    const [hasUnsavedOutputFields, setHasUnsavedOutputFields] = React.useState(false);
    const [projectId, setProjectId] = React.useState(null);
    
    // Load project context and issue types on component mount
    React.useEffect(() => {
        loadProjectContext();
    }, []);
    
    const loadProjectContext = async () => {
        setIsLoading(true);
        
        try {
            const contextResponse = await invoke('admin-resolver', {
                method: 'getCurrentProjectContext'
            });
            
            if (!contextResponse?.success || !contextResponse?.projectId) {
                throw new Error('Could not determine project context');
            }
            
            setProjectId(contextResponse.projectId);
            await loadProjectIssueTypes(contextResponse.projectId);
            
        } catch (error) {
            console.error('Failed to load project context:', error.message);
            setAvailableIssueTypes([]);
            setIsLoading(false);
        }
    };
    
    const loadProjectIssueTypes = async (projectId) => {
        try {
            const response = await invoke('admin-resolver', {
                method: 'getProjectIssueTypes',
                payload: { projectId }
            });
            
            if (response && response.success && response.issueTypes) {
                // Sort issue types in specific order
                const priorityOrder = ['story', 'bug', 'task', 'subtask', 'epic'];
                const sortedIssueTypes = response.issueTypes.sort((a, b) => {
                    const aName = a.name.toLowerCase();
                    const bName = b.name.toLowerCase();
                    
                    const aIndex = priorityOrder.indexOf(aName);
                    const bIndex = priorityOrder.indexOf(bName);
                    
                    if (aIndex !== -1 && bIndex !== -1) {
                        return aIndex - bIndex;
                    }
                    
                    if (aIndex !== -1) return -1;
                    if (bIndex !== -1) return 1;
                    
                    return a.name.localeCompare(b.name);
                });
                
                setAvailableIssueTypes(sortedIssueTypes);
                
                const issueTypeNames = sortedIssueTypes.map(it => it.name);
                await loadTemplatesForIssueTypes(issueTypeNames);
                await loadFieldConfigurationForIssueTypes(sortedIssueTypes);

            } else {
                throw new Error('Failed to load issue types');
            }
        } catch (error) {
            console.error('Failed to load issue types:', error);
            throw error;
        }
    };
    
    const loadTemplatesForIssueTypes = async (issueTypeNames) => {
        try {
            const response = await invoke('admin-resolver', {
                method: 'getTemplatesForIssueTypes',
                payload: { issueTypes: issueTypeNames }
            });
            
            if (response && response.success && response.templates) {
                setTemplates(response.templates);
                
                const firstIssueType = issueTypeNames.length > 0 ? issueTypeNames[0] : '';
                const firstTemplate = response.templates[firstIssueType] || '';
                
                setCurrentIssueType(firstIssueType);
                setCurrentTemplate(firstTemplate);
            } else {
                setCurrentTemplate('');
            }
        } catch (error) {
            console.error('Failed to load templates:', error);
            setCurrentTemplate('');
        } finally {
            setIsLoading(false);
        }
    };

    const loadFieldConfigurationForIssueTypes = async (issueTypesArray) => {
        try {
            const response = await invoke('admin-resolver', {
                method: 'getFieldConfigurationForIssueTypes',
                payload: { issueTypes: issueTypesArray }
            });
            
            if (response && response.success && response.issueTypeFieldData) {
                const fieldData = response.issueTypeFieldData;
                
                // Extract output fields mapping
                const outputFields = {};
                for (const [issueTypeName, data] of Object.entries(fieldData)) {
                    outputFields[issueTypeName] = data.selectedOutputField || '';
                }
                setOutputFields(outputFields);
                
                // Set current state for the first issue type
                const firstIssueType = issueTypesArray.length > 0 ? issueTypesArray[0].name : '';
                if (firstIssueType && fieldData[firstIssueType]) {
                    setCurrentOutputField(fieldData[firstIssueType].selectedOutputField || '');
                    setParagraphFields(fieldData[firstIssueType].paragraphFields || []);
                } else {
                    setCurrentOutputField('');
                    setParagraphFields([]);
                }
                
                // Store the complete field data for later use when switching issue types
                setFieldConfigurationData(fieldData);
            } else {
                setOutputFields({});
                setCurrentOutputField('');
                setParagraphFields([]);
                setFieldConfigurationData({});
            }
        } catch (error) {
            console.error('Failed to load field configuration:', error);
            setOutputFields({});
            setCurrentOutputField('');
            setParagraphFields([]);
            setFieldConfigurationData({});
        }
    };

    // Update current state when issue type changes
    React.useEffect(() => {
        if (currentIssueType && fieldConfigurationData[currentIssueType]) {
            const issueTypeData = fieldConfigurationData[currentIssueType];
            setCurrentOutputField(issueTypeData.selectedOutputField || '');
            setParagraphFields(issueTypeData.paragraphFields || []);
        }
    }, [currentIssueType, fieldConfigurationData]);
    
    const handleIssueTypeChange = (newIssueType) => {
        if (hasUnsavedChanges || hasUnsavedOutputFields) {
            const userConfirmed = window.confirm(
                'You have unsaved changes that will be lost if you switch issue types. Do you want to continue?'
            );
            
            if (!userConfirmed) {
                return;
            }
        }
        
        // Save current template and output field to memory before switching
        const updatedTemplates = { ...templates };
        updatedTemplates[currentIssueType] = currentTemplate;
        setTemplates(updatedTemplates);
        
        const updatedOutputFields = { ...outputFields };
        updatedOutputFields[currentIssueType] = currentOutputField;
        setOutputFields(updatedOutputFields);
        
        // Switch to new issue type
        setCurrentIssueType(newIssueType);
        setCurrentTemplate(templates[newIssueType] || '');
        setCurrentOutputField(outputFields[newIssueType] || '');
        setHasUnsavedChanges(false);
        setHasUnsavedOutputFields(false);
    };
    
    const handleTemplateChange = (value) => {
        setCurrentTemplate(value);
        if (!hasUnsavedChanges) {
            setHasUnsavedChanges(true);
        }
    };

    const handleOutputFieldChange = (value) => {
        setCurrentOutputField(value);
        if (!hasUnsavedOutputFields) {
            setHasUnsavedOutputFields(true);
        }
    };
    
    const handleSave = async () => {
        const template = currentTemplate.trim();
        
        if (!template) {
            showStatus('error', '❌ Template cannot be empty');
            return;
        }
        
        showStatus('info', `💾 Saving ${currentIssueType} configuration...`);
        
        try {
            // Save template
            const templateResponse = await invoke('admin-resolver', {
                method: 'saveTemplateByType',
                payload: {
                    issueType: currentIssueType,
                    template: template
                }
            });
            
            if (!templateResponse || !templateResponse.success) {
                showStatus('error', `❌ ${templateResponse?.error || 'Unknown error saving template'}`);
                return;
            }

            // Save output field if changed
            if (hasUnsavedOutputFields) {
                const outputFieldsToSave = {};
                outputFieldsToSave[currentIssueType] = currentOutputField;
                
                const outputFieldResponse = await invoke('admin-resolver', {
                    method: 'saveOutputFieldsForIssueTypes',
                    payload: {
                        outputFields: outputFieldsToSave
                    }
                });
                
                if (!outputFieldResponse || !outputFieldResponse.success) {
                    showStatus('error', `❌ ${outputFieldResponse?.error || 'Unknown error saving output field'}`);
                    return;
                }
            }
            
            // Update state on success
            const updatedTemplates = { ...templates };
            updatedTemplates[currentIssueType] = template;
            setTemplates(updatedTemplates);
            
            const updatedOutputFields = { ...outputFields };
            updatedOutputFields[currentIssueType] = currentOutputField;
            setOutputFields(updatedOutputFields);
            
            setHasUnsavedChanges(false);
            setHasUnsavedOutputFields(false);
            showStatus('success', `✅ ${currentIssueType} configuration saved successfully!`);
        } catch (error) {
            console.error('Failed to save configuration:', error);
            showStatus('error', `❌ Error saving configuration: ${error.message || 'Unknown error'}`);
        }
    };
    
    const handleReset = () => {
        setConfirmModal({
            isOpen: true,
            type: 'resetCurrent',
            data: { issueType: currentIssueType }
        });
    };
    
    const performResetCurrent = async (issueType) => {
        showStatus('info', `🔄 Resetting ${issueType} template to default...`);
        
        try {
            const response = await invoke('admin-resolver', {
                method: 'resetTemplateToDefault',
                payload: { issueType }
            });
            
            if (response && response.success && response.template !== undefined) {
                setCurrentTemplate(response.template);
                
                const updatedTemplates = { ...templates };
                updatedTemplates[issueType] = response.template;
                setTemplates(updatedTemplates);
                
                setHasUnsavedChanges(false);
                showStatus('success', `✅ ${issueType} template reset to default!`);
            } else {
                showStatus('error', `❌ ${response?.error || 'Unknown error'}`);
            }
        } catch (error) {
            console.error('Failed to reset template:', error);
            showStatus('error', `❌ Error resetting template: ${error.message || 'Unknown error'}`);
        }
    };
    
    const handleResetAll = () => {
        setConfirmModal({
            isOpen: true,
            type: 'resetAll',
            data: {}
        });
    };
    
    const performResetAll = async () => {
        showStatus('info', '🔄 Resetting all templates to defaults...');
        
        try {
            const response = await invoke('admin-resolver', {
                method: 'resetTemplatesToDefaults'
            });
            
            if (response && response.success) {
                setTemplates({});
                
                const issueTypeNames = availableIssueTypes.map(it => it.name);
                await loadTemplatesForIssueTypes(issueTypeNames);
                
                showStatus('success', '✅ All templates reset to defaults successfully!');
            } else {
                showStatus('error', `❌ ${response?.error || 'Unknown error'}`);
            }
        } catch (error) {
            console.error('Failed to reset all templates:', error);
            showStatus('error', `❌ Error resetting templates: ${error.message || 'Unknown error'}`);
        }
    };
    
    const handleConfirmModal = async () => {
        const modalType = confirmModal.type;
        const modalData = confirmModal.data;
        
        setConfirmModal({ isOpen: false, type: '', data: null });
        
        try {
            if (modalType === 'resetCurrent') {
                await performResetCurrent(modalData.issueType);
            } else if (modalType === 'resetAll') {
                await performResetAll();
            }
        } catch (error) {
            console.error('Modal operation failed:', error);
            showStatus('error', `❌ Operation failed: ${error.message || 'Unknown error'}`);
        }
    };
    
    const handleCancelModal = () => {
        setConfirmModal({ isOpen: false, type: '', data: null });
    };
    
    if (isLoading) {
        return (
            <div className="template-card">
                <div className="loading-state">
                    <p>Loading templates...</p>
                </div>
            </div>
        );
    }
    
    return (
        <div className="template-card">
            <TemplateHeader 
                currentIssueType={currentIssueType}
                onIssueTypeChange={handleIssueTypeChange}
                availableIssueTypes={availableIssueTypes}
                currentOutputField={currentOutputField}
                onOutputFieldChange={handleOutputFieldChange}
                paragraphFields={paragraphFields}
            />
            
            <p className="card-description">Define the structure for AI-expanded requirements. Use {'{curly brackets}'} for placeholders or instructions.</p>
            
            <TemplateEditor 
                value={currentTemplate}
                onChange={handleTemplateChange}
            />
            
            {(hasUnsavedChanges || hasUnsavedOutputFields) && (
                <div className="unsaved-warning">
                    ⚠️ You have unsaved changes that will be lost if you switch issue types.
                </div>
            )}
            
            <ButtonGroup 
                onSave={handleSave}
                onReset={handleReset}
                onResetAll={handleResetAll}
                hasUnsavedChanges={hasUnsavedChanges || hasUnsavedOutputFields}
            />
            
            <ConfirmationModal
                isOpen={confirmModal.isOpen}
                onClose={handleCancelModal}
                onConfirm={handleConfirmModal}
                title={
                    confirmModal.type === 'resetCurrent' 
                        ? `Reset ${confirmModal.data?.issueType || ''} Template`
                        : 'Reset All Templates'
                }
                message={
                    confirmModal.type === 'resetCurrent'
                        ? `Are you sure you want to reset the ${confirmModal.data?.issueType || ''} template to default? This action cannot be undone.`
                        : 'Are you sure you want to reset ALL templates to defaults? This will clear all customized templates, even for issue types that may have been removed from your project. This action cannot be undone.'
                }
                confirmText={
                    confirmModal.type === 'resetCurrent' ? 'Reset Template' : 'Reset All Templates'
                }
                cancelText="Cancel"
                isDangerous={true}
            />
        </div>
    );
}

function SettingsView() {
    const [settings, setSettings] = React.useState({
        aiModel: '',
        temperature: '0.7',
        maxTokens: '1000'
    });
    const [hasUnsavedChanges, setHasUnsavedChanges] = React.useState(false);
    const [isLoading, setIsLoading] = React.useState(true);
    
    // Load settings on component mount
    React.useEffect(() => {
        loadSettings();
    }, []);
    
    const loadSettings = async () => {
        setIsLoading(true);
        
        try {
            const response = await invoke('admin-resolver', {
                method: 'getSettings'
            });
            
            if (response && response.success && response.settings) {
                setSettings(response.settings);
            } else {
                showStatus('info', '💡 Using default settings');
            }
        } catch (error) {
            console.error('Failed to load settings:', error);
            showStatus('error', '❌ Error loading settings');
        } finally {
            setIsLoading(false);
        }
    };
    
    const handleSettingsChange = (field, value) => {
        setSettings(prev => ({
            ...prev,
            [field]: value
        }));
        
        if (!hasUnsavedChanges) {
            setHasUnsavedChanges(true);
        }
    };
    
    const handleSave = async () => {
        showStatus('info', '💾 Saving settings...');
        
        try {
            const response = await invoke('admin-resolver', {
                method: 'saveSettings',
                payload: {
                    settings: settings
                }
            });
            
            if (response && response.success) {
                setHasUnsavedChanges(false);
                showStatus('success', '✅ Settings saved successfully!');
            } else {
                showStatus('error', `❌ ${response?.error || 'Unknown error'}`);
            }
        } catch (error) {
            console.error('Failed to save settings:', error);
            showStatus('error', `❌ Error saving settings: ${error.message || 'Unknown error'}`);
        }
    };
    
    const handleReset = async () => {
        const confirmReset = window.confirm('Are you sure you want to reset all settings to defaults? This action cannot be undone.');
        if (!confirmReset) return;
        
        showStatus('info', '🔄 Resetting settings to defaults...');
        
        try {
            const response = await invoke('admin-resolver', {
                method: 'resetToDefaults'
            });
            
            if (response && response.success && response.settings) {
                setSettings(response.settings);
                setHasUnsavedChanges(false);
                showStatus('success', '✅ Settings reset to defaults successfully!');
            } else {
                showStatus('error', `❌ ${response?.error || 'Unknown error'}`);
            }
        } catch (error) {
            console.error('Failed to reset settings:', error);
            showStatus('error', `❌ Error resetting settings: ${error.message || 'Unknown error'}`);
        }
    };
    
    if (isLoading) {
        return (
            <div className="settings-card">
                <div className="loading-state">
                    <p>Loading settings...</p>
                </div>
            </div>
        );
    }
    
    return (
        <div className="settings-card">
            <div className="settings-header">
                <h2 className="card-title">AI Configuration</h2>
                <p className="card-description">Configure the AI model and parameters used for requirements expansion.</p>
            </div>
            
            <div className="settings-form">
                <div className="form-group">
                    <label htmlFor="aiModel" className="form-label">AI Model</label>
                    <input
                        type="text"
                        id="aiModel"
                        className="form-input"
                        value={settings.aiModel}
                        onChange={(e) => handleSettingsChange('aiModel', e.target.value)}
                        placeholder="e.g., deepseek/deepseek-chat-v3-0324:free"
                    />
                    <p className="form-help">The AI model identifier used for requirements expansion.</p>
                </div>
                
                <div className="form-group">
                    <label htmlFor="temperature" className="form-label">Temperature</label>
                    <div className="temperature-control">
                        <input
                            type="range"
                            id="temperature"
                            className="form-range"
                            min="0"
                            max="2"
                            step="0.1"
                            value={settings.temperature}
                            onChange={(e) => handleSettingsChange('temperature', e.target.value)}
                        />
                        <input
                            type="number"
                            className="form-input temperature-input"
                            min="0"
                            max="2"
                            step="0.1"
                            value={settings.temperature}
                            onChange={(e) => handleSettingsChange('temperature', e.target.value)}
                        />
                    </div>
                    <p className="form-help">Controls randomness: lower values = more focused, higher values = more creative.</p>
                </div>
                
                <div className="form-group">
                    <label htmlFor="maxTokens" className="form-label">Max Tokens</label>
                    <input
                        type="number"
                        id="maxTokens"
                        className="form-input"
                        min="1"
                        max="4000"
                        value={settings.maxTokens}
                        onChange={(e) => handleSettingsChange('maxTokens', e.target.value)}
                    />
                    <p className="form-help">Maximum number of tokens in the AI response (1-4000).</p>
                </div>
            </div>
            
            {hasUnsavedChanges && (
                <div className="unsaved-warning">
                    ⚠️ You have unsaved changes that will be lost if you switch tabs.
                </div>
            )}
            
            <div className="button-group">
                <button 
                    className="btn-primary"
                    onClick={handleSave}
                >
                    {hasUnsavedChanges ? 'Save Settings *' : 'Save Settings'}
                </button>
                <button 
                    className="btn-secondary"
                    onClick={handleReset}
                >
                    Reset to Defaults
                </button>
            </div>
        </div>
    );
}

function TemplateHeader({ currentIssueType, onIssueTypeChange, availableIssueTypes, currentOutputField, onOutputFieldChange, paragraphFields }) {
    // Helper to get field display name with fallback indicator
    const getFieldDisplayName = (field) => {
        return field.name;
    };

    // Get default field name with fallback logic
    const getDefaultFieldName = () => {
        // First try to find "AI Requirements" field
        const aiRequirementsField = paragraphFields.find(f => f.name === 'AI Requirements');
        if (aiRequirementsField) {
            return 'AI Requirements (default)';
        }
        
        // Fall back to Description field
        const descriptionField = paragraphFields.find(f => f.name === 'Description');
        if (descriptionField) {
            return 'Description (fallback)';
        }
        
        return 'Default field not available';
    };

    return (
        <div className="template-header">
            <div className="control-group">
                <label htmlFor="issueTypeSelect" className="issue-type-label">Issue Type:</label>
                <select 
                    id="issueTypeSelect"
                    className="issue-type-select"
                    value={currentIssueType}
                    onChange={(e) => onIssueTypeChange(e.target.value)}
                >
                    {availableIssueTypes.map(issueType => (
                        <option key={issueType.id} value={issueType.name}>
                            {issueType.name}
                        </option>
                    ))}
                </select>
            </div>
            
            <div className="control-group">
                <label htmlFor="outputFieldSelect" className="issue-type-label">Output Field:</label>
                <select 
                    id="outputFieldSelect"
                    className="issue-type-select"
                    value={currentOutputField}
                    onChange={(e) => onOutputFieldChange(e.target.value)}
                >
                    <option value="">
                        {getDefaultFieldName()}
                    </option>
                    {paragraphFields
                        .filter(field => {
                            // Exclude fields that are already shown as default
                            const aiRequirementsField = paragraphFields.find(f => f.name === 'AI Requirements');
                            const descriptionField = paragraphFields.find(f => f.name === 'Description');
                            
                            if (aiRequirementsField && field.name === 'AI Requirements') {
                                return false; // AI Requirements is shown as default
                            }
                            if (!aiRequirementsField && descriptionField && field.name === 'Description') {
                                return false; // Description is shown as fallback default
                            }
                            return true;
                        })
                        .map(field => (
                        <option key={field.id} value={field.name}>
                            {getFieldDisplayName(field)}
                        </option>
                    ))}
                </select>
            </div>
        </div>
    );
}

function TemplateEditor({ value, onChange }) {
    return (
        <div className="editor-container">
            <MDEditor
                value={value}
                onChange={(newValue) => onChange(newValue || '')}
                height={400}
                preview="live"
                hideToolbar={false}
                visibleDragBar={false}
                data-color-mode="light"
                placeholder="Loading template..."
            />
        </div>
    );
}

function ButtonGroup({ onSave, onReset, onResetAll, hasUnsavedChanges }) {
    return (
        <div className="button-group">
            <button 
                className="btn-primary"
                onClick={onSave}
            >
                {hasUnsavedChanges ? 'Save Template *' : 'Save Template'}
            </button>
            <button 
                className="btn-secondary"
                onClick={onReset}
            >
                Reset Current to Default
            </button>
            <button 
                className="btn-danger"
                onClick={onResetAll}
            >
                Reset All Templates
            </button>
        </div>
    );
}

function ConfirmationModal({ isOpen, onClose, onConfirm, title, message, confirmText = "Confirm", cancelText = "Cancel", isDangerous = false }) {
    if (!isOpen) return null;
    
    return (
        <div className="modal-overlay" onClick={onClose}>
            <div className="modal-content" onClick={(e) => e.stopPropagation()}>
                <div className="modal-header">
                    <h3 className="modal-title">{title}</h3>
                </div>
                <div className="modal-body">
                    <p className="modal-message">{message}</p>
                </div>
                <div className="modal-footer">
                    <button 
                        className="btn-secondary" 
                        onClick={onClose}
                    >
                        {cancelText}
                    </button>
                    <button 
                        className={isDangerous ? "btn-danger" : "btn-primary"}
                        onClick={onConfirm}
                    >
                        {confirmText}
                    </button>
                </div>
            </div>
        </div>
    );
}

function StatusMessage() {
    const [status, setStatus] = React.useState({ type: '', message: '' });
    
    React.useEffect(() => {
        window.showStatus = (type, message) => {
            setStatus({ type, message });
            
            if (type === 'success' || type === 'info') {
                setTimeout(() => {
                    setStatus({ type: '', message: '' });
                }, 5000);
            }
        };
    }, []);
    
    if (!status.message) return null;
    
    return (
        <div className="status-message">
            <div className={`status-${status.type}`}>
                {status.message}
            </div>
        </div>
    );
}

function showStatus(type, message) {
    if (window.showStatus) {
        window.showStatus(type, message);
    }
}

document.addEventListener('DOMContentLoaded', async function() {
    const root = document.getElementById('root');
    if (!root) {
        console.error('Root element not found');
        return;
    }
    
    try {
        await view.theme.enable();
        const reactRoot = createRoot(root);
        reactRoot.render(<AdminApp />);
    } catch (err) {
        console.warn('Failed to enable theming, continuing without design tokens:', err);
        const reactRoot = createRoot(root);
        reactRoot.render(<AdminApp />);
    }
});