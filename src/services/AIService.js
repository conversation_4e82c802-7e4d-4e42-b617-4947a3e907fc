/**
 * AI Service - Handles AI-powered requirements expansion
 */

import { callOpenRouter } from '../utils/openrouter-client.js';
import { createFullPrompt } from '../../build/system-prompt.js';
import { SettingsService } from './SettingsService.js';

export class AIService {
  constructor() {
    this.settingsService = new SettingsService();
  }
  
  /**
   * Expand simple requirements using AI
   * @param {string} issueTitle - The Jira issue title/summary
   * @param {string} simpleRequirements - The prompt text
   * @param {string} issueType - The issue type (story, task, bug) - optional for backward compatibility
   * @param {string} projectId - Project ID for project-scoped settings
   * @param {Object} options - Optional configuration overrides
   * @returns {Promise<string|null>} Expanded requirements or null if failed
   */
  async expandRequirements(issueTitle, simpleRequirements, issueType = null, projectId = null, options = {}) {
    try {
      if (!projectId) {
        throw new Error('Project ID is required for AI expansion');
      }
      
      const customTemplate = issueType 
        ? await this.settingsService.getTemplateForIssueType(issueType, projectId)
        : await this.settingsService.getTemplateForIssueType('story', projectId);
      
      const aiConfig = await this.settingsService.getAIConfig(projectId);
      
      const finalOptions = {
        model: aiConfig.model,
        temperature: aiConfig.temperature,
        maxTokens: aiConfig.maxTokens,
        apiEndpoint: aiConfig.apiEndpoint,
        ...options
      };
      
      const prompt = createFullPrompt(issueTitle, simpleRequirements, customTemplate);
      const result = await callOpenRouter(prompt, finalOptions);
      
      if (!result) {
        console.error('AI service returned null response');
        return null;
      }

      return result;
    } catch (error) {
      console.error('Error in AI service expansion:', error);
      throw error;
    }
  }
}