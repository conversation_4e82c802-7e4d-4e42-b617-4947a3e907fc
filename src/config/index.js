/**
 * Application Configuration
 * 
 * Centralized configuration with environment variable overrides.
 * This replaces scattered constants and provides a single source of truth.
 */

export const config = {
  // OpenRouter AI Service Configuration
  openrouter: {
    get apiKey() { return process.env[config.env.openrouterApiKey]; },
    get model() { return process.env.OPENROUTER_MODEL || 'deepseek/deepseek-chat-v3-0324:free'; },
    get temperature() { return parseFloat(process.env.OPENROUTER_TEMPERATURE) || 0.7; },
    get maxTokens() { return parseInt(process.env.OPENROUTER_MAX_TOKENS) || 1000; },
    get apiEndpoint() { return process.env.OPENROUTER_API_ENDPOINT || 'https://openrouter.ai/api/v1/chat/completions'; },
    baseUrl: 'https://openrouter.ai',
    headers: {
      httpReferer: 'https://project0jiraplugin.atlassian.net',
      xTitle: 'Jira Requirements Expander'
    }
  },

  // Jira endpoints are hardcoded since they can't be used with Forge's route template literals

  // Field Names Configuration
  fields: {
    summary: 'Summary',
    prompt: 'Prompt',
    aiRequirements: 'AI Requirements'
  },

  // Event and Queue Configuration
  events: {
    jiraIssueUpdated: 'avi:jira:updated:issue',
    queues: {
      requirements: 'requirements-queue'
    },
    handlers: {
      promptUpdated: 'promptUpdateHandler',
      requirementsProcessor: 'requirementsProcessorHandler'
    }
  },

  // Environment Variable Names
  env: {
    openrouterApiKey: 'SECRET_OPENROUTER_API_KEY'
  },

  // Chat Configuration
  chat: {
    roles: {
      user: 'user',
      assistant: 'assistant',
      system: 'system'
    }
  },

  // Storage Configuration
  storage: {
    keys: {
      // AI Settings
      aiModel: 'ai-settings-model',
      temperature: 'ai-settings-temperature',
      maxTokens: 'ai-settings-max-tokens',
      
      // Template Keys
      templatePrefix: 'ai-template-',
      
      // Output Field Configuration Keys
      outputFieldPrefix: 'output-field-'
    }
  },

  // Regular Expressions
  patterns: {
    markdownCodeBlock: /^```(?:markdown)?\s*\n([\s\S]*?)\n```$/
  }
};

