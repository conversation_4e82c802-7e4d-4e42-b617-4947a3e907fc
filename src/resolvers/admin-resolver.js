/**
 * Admin Resolver
 * 
 * Handles admin UI interactions for settings management.
 * Provides methods for loading, saving, and resetting admin settings.
 */

import { SettingsService } from '../services/SettingsService.js';
import { JiraService } from '../services/JiraService.js';

/**
 * Admin handler for settings UI
 */
export const adminHandler = async (req) => {
  const settingsService = new SettingsService();

  try {
    // Handle Forge bridge call format - method and payload are nested in req.call.payload
    const method = req.call.payload.method;
    const payload = req.call.payload.payload;
    switch (method) {
      case 'getSettings':
        return await handleGetSettings(settingsService, req);
      
      case 'saveSettings':
        return await handleSaveSettings(settingsService, payload, req);
      
      case 'resetToDefaults':
        return await handleResetToDefaults(settingsService, req);
      
      // New issue type-specific template methods
      case 'getAllTemplates':
        return await handleGetAllTemplates(settingsService, req);
      
      case 'getTemplateByType':
        return await handleGetTemplateByType(settingsService, payload, req);
      
      case 'saveTemplateByType':
        return await handleSaveTemplateByType(settingsService, payload, req);
      
      case 'resetTemplatesToDefaults':
        return await handleResetTemplatesToDefaults(settingsService, req);
      
      case 'getProjectIssueTypes':
        return await handleGetProjectIssueTypes(payload);
      
      case 'getTemplatesForIssueTypes':
        return await handleGetTemplatesForIssueTypes(settingsService, payload, req);
      
      case 'resetTemplateToDefault':
        return await handleResetTemplateToDefault(settingsService, payload, req);
      
      case 'getCurrentProjectContext':
        return await handleGetCurrentProjectContext(req);
      
      // Output field configuration methods
      case 'getParagraphFieldsForIssueType':
        return await handleGetParagraphFieldsForIssueType(payload);
      
      case 'getOutputFieldsForIssueTypes':
        return await handleGetOutputFieldsForIssueTypes(settingsService, payload, req);
      
      case 'getFieldConfigurationForIssueTypes':
        return await handleGetFieldConfigurationForIssueTypes(settingsService, payload, req);
      
      case 'saveOutputFieldsForIssueTypes':
        return await handleSaveOutputFieldsForIssueTypes(settingsService, payload, req);
      
      default:
        return {
          success: false,
          error: `Unknown method: ${method}`
        };
    }
  } catch (error) {
    console.error('Error in admin handler:', error);
    return {
      success: false,
      error: error.message || 'Internal server error'
    };
  }
};

function getProjectIdFromRequest(req) {
  const projectId = req.context?.extension?.project?.id;
  if (!projectId) {
    throw new Error('Project ID not found in request context');
  }
  return projectId;
}

function createSuccessResponse(data) {
  return { success: true, ...data };
}

function createErrorResponse(error, defaultMessage) {
  return {
    success: false,
    error: defaultMessage || error.message || 'Unknown error'
  };
}

async function handleWithErrorHandling(operation, errorMessage) {
  try {
    return await operation();
  } catch (error) {
    console.error(`${errorMessage}:`, error);
    return createErrorResponse(error, errorMessage);
  }
}

async function handleGetSettings(settingsService, req) {
  return handleWithErrorHandling(async () => {
    const projectId = getProjectIdFromRequest(req);
    const settings = await settingsService.getSettings(projectId);
    return createSuccessResponse({ settings });
  }, 'Failed to load settings');
}

async function handleSaveSettings(settingsService, payload, req) {
  return handleWithErrorHandling(async () => {
    const projectId = getProjectIdFromRequest(req);
    
    if (!payload || !payload.settings) {
      return createErrorResponse(new Error('Settings data is required'));
    }

    const validationError = validateSettings(payload.settings);
    if (validationError) {
      return createErrorResponse(new Error(validationError));
    }

    const success = await settingsService.saveSettings(payload.settings, projectId);
    
    if (success) {
      return createSuccessResponse({ message: 'Settings saved successfully' });
    } else {
      return createErrorResponse(new Error('Failed to save settings'));
    }
  }, 'Failed to save settings');
}

/**
 * Handle reset to defaults request
 */
async function handleResetToDefaults(settingsService, req) {
  return handleWithErrorHandling(async () => {
    const projectId = getProjectIdFromRequest(req);
    const settings = await settingsService.resetToDefaults(projectId);
    return createSuccessResponse({ settings, message: 'Settings reset to defaults' });
  }, 'Failed to reset settings');
}

function validateSettings(settings) {
  // Validate story template
  if (settings.storyTemplate !== undefined) {
    if (typeof settings.storyTemplate !== 'string') {
      return 'Story template must be a string';
    }
    if (settings.storyTemplate.trim().length === 0) {
      return 'Story template cannot be empty';
    }
  }

  // Validate AI model
  if (settings.aiModel !== undefined) {
    if (typeof settings.aiModel !== 'string') {
      return 'AI model must be a string';
    }
  }

  // Validate temperature
  if (settings.temperature !== undefined) {
    const temp = parseFloat(settings.temperature);
    if (isNaN(temp) || temp < 0 || temp > 2) {
      return 'Temperature must be a number between 0 and 2';
    }
  }

  // Validate max tokens
  if (settings.maxTokens !== undefined) {
    const tokens = parseInt(settings.maxTokens);
    if (isNaN(tokens) || tokens < 1 || tokens > 4000) {
      return 'Max tokens must be a number between 1 and 4000';
    }
  }


  return null; // No validation errors
}

async function handleGetAllTemplates(settingsService, req) {
  return handleWithErrorHandling(async () => {
    const projectId = getProjectIdFromRequest(req);
    const templates = await settingsService.getAllTemplates(projectId);
    return createSuccessResponse({ templates });
  }, 'Failed to load templates');
}

async function handleGetTemplateByType(settingsService, payload, req) {
  return handleWithErrorHandling(async () => {
    const projectId = getProjectIdFromRequest(req);
    
    if (!payload || !payload.issueType) {
      return createErrorResponse(new Error('Issue type is required'));
    }

    const template = await settingsService.getTemplateForIssueType(payload.issueType, projectId);
    return createSuccessResponse({ template, issueType: payload.issueType });
  }, 'Failed to load template');
}

/**
 * Handle save template by type request
 */
async function handleSaveTemplateByType(settingsService, payload, req) {
  try {
    const projectId = getProjectIdFromRequest(req);
    
    if (!payload || !payload.issueType || !payload.template) {
      return {
        success: false,
        error: 'Issue type and template are required'
      };
    }

    // Validate template
    const validationError = validateTemplate(payload.template);
    if (validationError) {
      return {
        success: false,
        error: validationError
      };
    }

    const success = await settingsService.saveTemplateForIssueType(payload.issueType, payload.template, projectId);
    
    if (success) {
      return {
        success: true,
        message: `Template saved successfully for ${payload.issueType}`
      };
    } else {
      return {
        success: false,
        error: 'Failed to save template'
      };
    }
  } catch (error) {
    console.error(`Error saving template for issue type ${payload?.issueType}:`, error);
    return {
      success: false,
      error: 'Failed to save template'
    };
  }
}

/**
 * Handle reset templates to defaults request
 */
async function handleResetTemplatesToDefaults(settingsService, req) {
  try {
    const projectId = getProjectIdFromRequest(req);
    const defaultTemplates = await settingsService.resetTemplatesToDefaults(projectId);
    return {
      success: true,
      templates: defaultTemplates,
      message: 'All templates reset to defaults'
    };
  } catch (error) {
    console.error('Error resetting templates to defaults:', error);
    return {
      success: false,
      error: `Failed to reset templates: ${error.message || 'Unknown error'}`
    };
  }
}

/**
 * Validate template content
 */
function validateTemplate(template) {
  if (typeof template !== 'string') {
    return 'Template must be a string';
  }
  if (template.trim().length === 0) {
    return 'Template cannot be empty';
  }
  if (template.length > 10000) {
    return 'Template is too long (maximum 10,000 characters)';
  }
  return null; // No validation errors
}


/**
 * Handle get project issue types request
 */
async function handleGetProjectIssueTypes(payload) {
  try {
    if (!payload || !payload.projectId) {
      return {
        success: false,
        error: 'Project ID is required'
      };
    }

    const jiraService = new JiraService();
    const issueTypes = await jiraService.getProjectIssueTypes(payload.projectId);
    
    return {
      success: true,
      issueTypes
    };
  } catch (error) {
    console.error(`Error getting issue types for project ${payload?.projectId}:`, error);
    return {
      success: false,
      error: 'Failed to load project issue types'
    };
  }
}

/**
 * Handle get templates for multiple issue types request
 */
async function handleGetTemplatesForIssueTypes(settingsService, payload, req) {
  try {
    const projectId = getProjectIdFromRequest(req);
    
    if (!payload || !payload.issueTypes || !Array.isArray(payload.issueTypes)) {
      return {
        success: false,
        error: 'Issue types array is required'
      };
    }

    const templates = await settingsService.getTemplatesForIssueTypes(payload.issueTypes, projectId);
    
    return {
      success: true,
      templates
    };
  } catch (error) {
    console.error(`Error getting templates for issue types:`, error);
    return {
      success: false,
      error: 'Failed to load templates for issue types'
    };
  }
}

/**
 * Handle reset single template to default request
 */
async function handleResetTemplateToDefault(settingsService, payload, req) {
  try {
    const projectId = getProjectIdFromRequest(req);
    
    if (!payload || !payload.issueType) {
      return {
        success: false,
        error: 'Issue type is required'
      };
    }

    // Delete the stored template for this specific issue type
    await settingsService.deleteTemplateForIssueType(payload.issueType, projectId);
    
    // Get the default template for this issue type
    const defaultTemplate = settingsService.getDefaultTemplateForIssueType(payload.issueType);
    
    return {
      success: true,
      template: defaultTemplate,
      issueType: payload.issueType,
      message: `Template reset to default for ${payload.issueType}`
    };
  } catch (error) {
    console.error(`Error resetting template for issue type ${payload?.issueType}:`, error);
    return {
      success: false,
      error: 'Failed to reset template to default'
    };
  }
}

/**
 * Handle get current project context request
 */
async function handleGetCurrentProjectContext(req) {
  try {
    // Project pages have access to project context via extension data
    const context = req.context;
    
    let projectId = null;
    
    // For jira:projectPage, the project ID is available in extension.project.id
    if (context?.extension?.project?.id) {
      projectId = context.extension.project.id;
    }
    
    if (!projectId) {
      console.error('No project ID found in project page context');
      return {
        success: false,
        error: 'Could not determine project context from project page'
      };
    }
    
    
    return {
      success: true,
      projectId: projectId
    };
  } catch (error) {
    console.error('Error getting project context:', error.message);
    return {
      success: false,
      error: 'Failed to get project context'
    };
  }
}

/**
 * Handle get paragraph fields for issue type request
 */
async function handleGetParagraphFieldsForIssueType(payload) {
  try {
    if (!payload || !payload.projectId || !payload.issueTypeId) {
      return {
        success: false,
        error: 'Project ID and Issue Type ID are required'
      };
    }

    const jiraService = new JiraService();
    const paragraphFields = await jiraService.getParagraphFieldsForProjectAndIssueType(payload.projectId, payload.issueTypeId);
    
    return {
      success: true,
      paragraphFields
    };
  } catch (error) {
    console.error(`Error getting paragraph fields for project ${payload?.projectId}, issue type ${payload?.issueTypeId}:`, error);
    return {
      success: false,
      error: 'Failed to load paragraph fields for issue type'
    };
  }
}

/**
 * Handle get output field configurations for multiple issue types request
 */
async function handleGetOutputFieldsForIssueTypes(settingsService, payload, req) {
  try {
    const projectId = getProjectIdFromRequest(req);
    
    if (!payload || !payload.issueTypes || !Array.isArray(payload.issueTypes)) {
      return {
        success: false,
        error: 'Issue types array is required'
      };
    }

    const outputFields = await settingsService.getOutputFieldsForIssueTypes(payload.issueTypes, projectId);
    
    return {
      success: true,
      outputFields
    };
  } catch (error) {
    console.error(`Error getting output fields for issue types:`, error);
    return {
      success: false,
      error: 'Failed to load output fields for issue types'
    };
  }
}

/**
 * Handle get complete field configuration for multiple issue types request
 * Returns both available paragraph fields and current output field selection for each issue type
 */
async function handleGetFieldConfigurationForIssueTypes(settingsService, payload, req) {
  try {
    const projectId = getProjectIdFromRequest(req);
    
    if (!payload || !payload.issueTypes || !Array.isArray(payload.issueTypes)) {
      return {
        success: false,
        error: 'Issue types array is required'
      };
    }

    const jiraService = new JiraService();
    const issueTypeFieldData = {};

    // Process all issue types in parallel for better performance
    const issueTypePromises = payload.issueTypes.map(async (issueType) => {
      try {
        // Get both paragraph fields and output field configuration in parallel
        const [paragraphFields, outputFields] = await Promise.all([
          jiraService.getParagraphFieldsForProjectAndIssueType(projectId, issueType.id),
          settingsService.getOutputFieldsForIssueTypes([issueType.name], projectId)
        ]);

        const selectedOutputField = outputFields[issueType.name] || '';

        return {
          issueTypeName: issueType.name,
          data: {
            paragraphFields,
            selectedOutputField,
            issueTypeId: issueType.id
          }
        };
      } catch (error) {
        console.error(`Error processing issue type ${issueType.name}:`, error);
        // Return error data instead of throwing
        return {
          issueTypeName: issueType.name,
          data: {
            paragraphFields: [],
            selectedOutputField: '',
            issueTypeId: issueType.id,
            error: `Failed to load data for ${issueType.name}`
          }
        };
      }
    });

    // Wait for all issue types to be processed
    const results = await Promise.all(issueTypePromises);

    // Build the final data structure
    results.forEach(result => {
      issueTypeFieldData[result.issueTypeName] = result.data;
    });

    return {
      success: true,
      issueTypeFieldData
    };
  } catch (error) {
    console.error(`Error getting field configuration for issue types:`, error);
    return {
      success: false,
      error: 'Failed to load field configuration for issue types'
    };
  }
}

/**
 * Handle save output field configurations for multiple issue types request
 */
async function handleSaveOutputFieldsForIssueTypes(settingsService, payload, req) {
  try {
    const projectId = getProjectIdFromRequest(req);
    
    if (!payload || !payload.outputFields || typeof payload.outputFields !== 'object') {
      return {
        success: false,
        error: 'Output fields configuration is required'
      };
    }

    const savePromises = [];
    for (const [issueType, fieldName] of Object.entries(payload.outputFields)) {
      if (fieldName && fieldName.trim() !== '') {
        savePromises.push(settingsService.saveOutputFieldForIssueType(issueType, fieldName, projectId));
      } else {
        // If field name is empty, delete the configuration to use default
        savePromises.push(settingsService.deleteOutputFieldForIssueType(issueType, projectId));
      }
    }

    const results = await Promise.all(savePromises);
    const allSuccessful = results.every(result => result === true);
    
    if (allSuccessful) {
      return {
        success: true,
        message: 'Output field configurations saved successfully'
      };
    } else {
      return {
        success: false,
        error: 'Failed to save some output field configurations'
      };
    }
  } catch (error) {
    console.error(`Error saving output fields:`, error);
    return {
      success: false,
      error: 'Failed to save output field configurations'
    };
  }
}